import { CommonModule, isPlatformBrowser } from '@angular/common';
import { Component, effect, inject, Inject, Input, PLATFORM_ID, Signal } from '@angular/core';
import { OnInit, OnDestroy } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Track } from '@/interfaces/track';
import { HttpClient } from '@angular/common/http';
import { AudioService } from "@/services/audio.service";
import { ToasterService } from '@/services/toaster.service';
import { Router } from '@angular/router';
import { ShareDataService } from '@/services/share-data.service';
import { PlaylistService } from '@/services/playlist.service';
import { Fancybox } from "@fancyapps/ui";
import { ProfileService } from '@/services/profile.service';
import { NgSelectModule } from "@ng-select/ng-select";

@Component({
  selector: 'app-audio-player',
  standalone: true,
  imports: [CommonModule, FormsModule, NgSelectModule],
  templateUrl: './audio-player.component.html',
  styleUrl: './audio-player.component.scss'
})
export class AudioPlayerComponent implements OnInit, OnDestroy {
  isShuffle: boolean = false;
  isPlaying: boolean = false;
  currentTime: number = 0;
  isMuted: boolean = false;
  volume: number = 0.5;
  audio: HTMLAudioElement | null = null;
  toasterService = inject(ToasterService);
  router = inject(Router);
  @Input() track!: Signal<Track>;
  @Input() set play(value: any) {
    if (this.isPlaying && this.audio) {
      this.audio.pause();
    }
    this.playTrack();
  }
  progressBarWidth: string = '0';
  duration: number = 0;
  audioService = inject(AudioService);
  playlistService = inject(PlaylistService);
  profileService = inject(ProfileService);
  selectedPlaylists: any = []
  selectedTrackId: number | null = null;
  listened: boolean = false;

  constructor(
    @Inject(PLATFORM_ID) private platformId: any,
    private http: HttpClient,
    private shareDataService: ShareDataService
  ) {
    effect(() => {
      const currentTrack = this.track();
      if (currentTrack && isPlatformBrowser(this.platformId)) {
        this.handleTrackChange(currentTrack);
      }

    });
   }


  ngOnInit() {
    if (isPlatformBrowser(this.platformId)) {
      this.audio = new Audio();
      this.addAudioEventListeners();
    }
    this.shareDataService.playMainPlayer$.subscribe(() => {
      if (this.isPlaying) {
        if (this.audio) {
          this.audio.pause();
          this.isPlaying = false;
        }
      }
    })

  }

  private handleTrackChange(newTrack: Track) {
    if (!this.audio) return;
    
    this.currentTime = 0;
    this.progressBarWidth = '0%';
    
    this.audio.src = newTrack.link;
    
    this.audio.currentTime = 0;
    
    this.audio.play().then(() => {
      this.isPlaying = true;
      this.shareDataService.playCard();
    });
  }

  addToPlaylistProfile() {
    for (let playlist of this.selectedPlaylists) {
      this.playlistService.add(playlist, this.selectedTrackId).subscribe(() => {
        this.selectedPlaylists = []
        Fancybox.close()
        this.toasterService.showToast('Лекция добавлена в Избранное', 'success', 'bottom-middle', 3000);
      })
    }
  }

  addAudioEventListeners() {
    if (this.audio) {
      this.audio.addEventListener('timeupdate', this.updateTime.bind(this));
      this.audio.addEventListener('loadedmetadata', this.updateDuration.bind(this));
    }
  }

  updateTime() {
    if (isPlatformBrowser(this.platformId) && this.audio) {
      this.currentTime = this.audio.currentTime;
      const progressPercentage = (this.audio.currentTime / this.audio.duration) * 100;
      this.progressBarWidth = `${progressPercentage}%`;
      if (Math.ceil(this.currentTime) % 2 === 0) {
        if (progressPercentage > 90 && !this.listened && this.isPlaying) {
          this.listened = true
          this.audioService.setAsListened(this.track().id).subscribe()
        }
      }
    }
  }

  private getZoomFactor(): number {
    // Check if we're in a zoomed container
    const contentWrapper = document.querySelector('.content-height_wrap:not(.main-contetnt-wrapper)');
    if (contentWrapper) {
      return 0.8; // zoom: 0.8 from styles.scss
    }
    return 1;
  }

  seekTo(event: MouseEvent) {
    if (this.audio && this.audio.duration) {
      const progressWrapper = event.target as HTMLElement;
      const zoomFactor = this.getZoomFactor();

      // Use offsetX but adjust for zoom factor
      let clickX = event.offsetX;

      // Adjust for zoom factor - when zoomed to 0.8, offsetX is compressed
      if (zoomFactor !== 1) {
        clickX = clickX / zoomFactor;
      }

      const progressWidth = progressWrapper.clientWidth;
      const newTime = (clickX / progressWidth) * this.audio.duration;
      this.audio.currentTime = newTime;
    }
  }

  get progressPercentage(): number {
    return (this.currentTime / this.duration) * 100 || 0;
  }

  updateDuration() {
    if (isPlatformBrowser(this.platformId) && this.audio) {
      this.duration = this.audio.duration;
    }
  }

  addToPlaylist() {
    this.shareDataService.addToPlaylist(this.track());
    this.toasterService.showToast('Лекция добавлена в плейлист!', 'success', 'bottom-middle', 3000);
  }

  toggleMute() {
    if (isPlatformBrowser(this.platformId) && this.audio) {
      this.audio.muted = !this.audio.muted;
      this.isMuted = !this.isMuted;
    }
  }

  share() {
    // navigator.clipboard.writeText(this.track.link).then(() => {
    navigator.clipboard.writeText('https://dev.advayta.org/' + this.router.url).then(() => {
      this.toasterService.showToast('Ссылка на страницу скопирована в буфер обмена!', 'success', 'bottom-middle', 3000);
    }).catch((error) => {
      this.toasterService.showToast('Ошибка копирования.', 'error', 'bottom-middle', 3000);
    });
  }

  adjustVolume(event: any) {
    if (isPlatformBrowser(this.platformId) && this.audio) {
      const volume = event.target.value;
      this.volume = volume;
      this.audio.volume = this.volume;
    }
  }

  ngOnDestroy() {
    if (this.audio) {
      this.audio.pause();
      this.audio = null;
    }
  }

  formatTime(time: number | undefined): string {
    if (!time || isNaN(time)) return '00:00';
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  }

  togglePlay() {
    if (isPlatformBrowser(this.platformId) && this.audio) {
      if (!this.audio.src) {
        this.audio.src = this.track().link;
      }
      if (this.isPlaying) {
        this.audio.pause();
      } else {
        this.audio.play().then(() => {
          this.isPlaying = true;
          this.shareDataService.playCard();
        }).catch((error) => {
          if (error.name === 'AbortError') {
            console.warn("Play was interrupted:", error);
          } else {
            console.error("Play error:", error);
          }
        });
      }
    }
    this.isPlaying = !this.isPlaying;
  }

  nextTrack() {
    this.playTrack();
  }

  prevTrack() {
    this.playTrack();
  }

  playTrack() {
    if (isPlatformBrowser(this.platformId) && this.audio) {
      this.audio.src = this.track().link;
      this.audio.play();
      this.isPlaying = true;
    }
  }

  shuffle() {
    this.isShuffle = !this.isShuffle;
  }
}
